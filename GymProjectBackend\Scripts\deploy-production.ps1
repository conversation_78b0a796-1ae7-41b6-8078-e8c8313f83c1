# ========================================
# PRODUCTION ENVIRONMENT DEPLOY SCRIPT
# ========================================

Write-Host "🚀 PRODUCTION'A DEPLOY BAŞLIYOR..." -ForegroundColor Green
Write-Host "📍 Target: https://admin.gymkod.com" -ForegroundColor Cyan
Write-Host "🗃️ Database: GymProject (CANLI VERİTABANI)" -ForegroundColor Red
Write-Host "👥 Etkilenecek Kullanıcı: 10.000+" -ForegroundColor Red
Write-Host "" -ForegroundColor White

# Güvenlik onayı
Write-Host "⚠️ UYARI: PRODUCTION ENVIRONMENT'A DEPLOY EDİYORSUNUZ!" -ForegroundColor Red -BackgroundColor Yellow
Write-Host "Bu işlem canlı sistemi etki<PERSON>ecektir." -ForegroundColor Red
Write-Host "" -ForegroundColor White

$confirmation = Read-Host "Devam etmek için 'EVET' yazın (büyük harflerle)"
if ($confirmation -ne 'EVET') {
    Write-Host "❌ Deploy İptal Edildi" -ForegroundColor Red
    Write-Host "💡 Güvenlik için iptal edildi. Devam etmek için 'EVET' yazmanız gerekiyor." -ForegroundColor Yellow
    exit 0
}

Write-Host "" -ForegroundColor White
Write-Host "✅ Onay alındı, deploy başlıyor..." -ForegroundColor Green

# Production environment variables set et
Write-Host "📝 Environment Variables Set Ediliyor..." -ForegroundColor Yellow
$env:ASPNETCORE_ENVIRONMENT = "Production"
$env:DB_SERVER = "localhost"
$env:DB_PASSWORD = "1265222500Aa"
$env:DB_DATABASE = "GymProject"

Write-Host "✅ Environment Variables:" -ForegroundColor Green
Write-Host "   - ASPNETCORE_ENVIRONMENT: $env:ASPNETCORE_ENVIRONMENT" -ForegroundColor White
Write-Host "   - DB_SERVER: $env:DB_SERVER" -ForegroundColor White
Write-Host "   - DB_DATABASE: $env:DB_DATABASE" -ForegroundColor White
Write-Host "   - DB_PASSWORD: ********" -ForegroundColor White
Write-Host "" -ForegroundColor White

# Backup tarihi
$backupDate = Get-Date -Format "yyyyMMdd_HHmmss"
Write-Host "💾 Backup Tarihi: $backupDate" -ForegroundColor Yellow

# Solution dizinine git
Set-Location -Path $PSScriptRoot\..
Write-Host "📂 Working Directory: $(Get-Location)" -ForegroundColor Yellow

# Mevcut API'yi backup'la
$apiPath = "C:\Users\<USER>\Desktop\GymProject\api"
$backupPath = "C:\Users\<USER>\Desktop\GymProject\api-backups"

Write-Host "💾 Mevcut API Backup'lanıyor..." -ForegroundColor Yellow
if (Test-Path $apiPath) {
    if (!(Test-Path $backupPath)) {
        New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
    }
    $backupFullPath = "$backupPath\api-backup-$backupDate"
    Copy-Item $apiPath $backupFullPath -Recurse -Force
    Write-Host "✅ Backup: $backupFullPath" -ForegroundColor Green
} else {
    Write-Host "⚠️ Mevcut API klasörü bulunamadı, backup atlanıyor..." -ForegroundColor Yellow
}

# Clean previous builds
Write-Host "🧹 Önceki Build'ler Temizleniyor..." -ForegroundColor Yellow
dotnet clean --configuration Release --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ Clean işlemi başarısız, devam ediliyor..." -ForegroundColor Yellow
}

# Restore packages
Write-Host "📦 NuGet Packages Restore Ediliyor..." -ForegroundColor Yellow
dotnet restore --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Package Restore Hatası!" -ForegroundColor Red
    Write-Host "🔄 Backup'tan geri yükleniyor..." -ForegroundColor Yellow
    if (Test-Path $backupFullPath) {
        Remove-Item $apiPath -Recurse -Force -ErrorAction SilentlyContinue
        Copy-Item $backupFullPath $apiPath -Recurse -Force
        Write-Host "✅ Backup'tan geri yüklendi" -ForegroundColor Green
    }
    exit 1
}

# Build project
Write-Host "🔨 Proje Build Ediliyor..." -ForegroundColor Yellow
dotnet build --configuration Release --no-restore --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build Hatası!" -ForegroundColor Red
    Write-Host "🔄 Backup'tan geri yükleniyor..." -ForegroundColor Yellow
    if (Test-Path $backupFullPath) {
        Remove-Item $apiPath -Recurse -Force -ErrorAction SilentlyContinue
        Copy-Item $backupFullPath $apiPath -Recurse -Force
        Write-Host "✅ Backup'tan geri yüklendi" -ForegroundColor Green
    }
    exit 1
}

# API klasörünü hazırla
Write-Host "📁 API Klasörü Hazırlanıyor: $apiPath" -ForegroundColor Yellow
if (Test-Path $apiPath) {
    Remove-Item "$apiPath\*" -Recurse -Force -ErrorAction SilentlyContinue
} else {
    New-Item -ItemType Directory -Path $apiPath -Force | Out-Null
}

# Portable publish
Write-Host "📦 Portable Publish Ediliyor..." -ForegroundColor Yellow
dotnet publish WebAPI/WebAPI.csproj `
    --configuration Release `
    --framework net8.0 `
    --runtime portable `
    --output $apiPath `
    --self-contained false `
    --no-build `
    --verbosity quiet

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Publish Hatası!" -ForegroundColor Red
    Write-Host "🔄 Backup'tan geri yükleniyor..." -ForegroundColor Yellow
    if (Test-Path $backupFullPath) {
        Remove-Item $apiPath -Recurse -Force -ErrorAction SilentlyContinue
        Copy-Item $backupFullPath $apiPath -Recurse -Force
        Write-Host "✅ Backup'tan geri yüklendi" -ForegroundColor Green
    }
    exit 1
}

# Dosya sayısını kontrol et
$fileCount = (Get-ChildItem -Path $apiPath -Recurse | Measure-Object).Count
Write-Host "📊 Publish edilen dosya sayısı: $fileCount" -ForegroundColor Green

# Başarı mesajı
Write-Host "" -ForegroundColor White
Write-Host "✅ PRODUCTION DEPLOY TAMAMLANDI!" -ForegroundColor Green
Write-Host "📁 Dosyalar: $apiPath" -ForegroundColor Cyan
Write-Host "🌍 Site: https://admin.gymkod.com" -ForegroundColor Cyan
Write-Host "💾 Backup: $backupFullPath" -ForegroundColor Cyan
Write-Host "💡 Sunucuda çalıştırma: dotnet api/WebAPI.dll" -ForegroundColor Yellow
Write-Host "" -ForegroundColor White
Write-Host "🔄 Sonraki Adımlar:" -ForegroundColor Magenta
Write-Host "   1. API klasörünü sunucuya kopyalayın" -ForegroundColor White
Write-Host "   2. Sunucuda: dotnet api/WebAPI.dll" -ForegroundColor White
Write-Host "   3. https://admin.gymkod.com adresinden test edin" -ForegroundColor White
Write-Host "   4. Sorun varsa backup'tan geri yükleyin" -ForegroundColor White
Write-Host "" -ForegroundColor White
