# 🚀 GymKod Manual Deployment Guide

## 📍 **MANUEL DEPLOYMENT İÇİN BASIT KULLANIM**

Sen deployment'ı manuel ya<PERSON>n için sadece environment setup'a ihtiyacın var!

```
C:\Users\<USER>\Desktop\GymProject\  ← BURADAN çalıştır!
├── GymProjectBackend/
├── GymProjectFrontend/
├── set-development.ps1             ← Environment setup script'leri
├── set-staging.ps1
└── set-production.ps1
```

## 🎯 **MANUEL DEPLOYMENT ADIMLARI**

### **1. Development (Geliştirme)**
```powershell
# 1. Environment'ı set et
.\set-development.ps1

# 2. Frontend build et (VSCode'da)
cd GymProjectFrontend
ng build --configuration=development

# 3. Backend publish et
cd GymProjectBackend
dotnet publish --configuration Debug

# 4. Çalıştır
dotnet run --project WebAPI
```

### **2. Staging (Test)**
```powershell
# 1. Environment'ı set et
.\set-staging.ps1

# 2. Frontend build et (VSCode'da)
cd GymProjectFrontend
ng build --configuration=staging

# 3. Backend publish et
cd GymProjectBackend
dotnet publish --configuration Release

# 4. staging.gymkod.com'a kopyala
```

### **3. Production (Canlı)**
```powershell
# 1. Environment'ı set et (DİKKATLİ!)
.\set-production.ps1

# 2. Frontend build et (VSCode'da)
cd GymProjectFrontend
ng build --configuration=production

# 3. Backend publish et
cd GymProjectBackend
dotnet publish --configuration Release

# 4. admin.gymkod.com'a kopyala
```

## 🔧 **ENVIRONMENT SETUP SCRIPT'LERİ**

### **Sadece Backend Build Et:**
```powershell
.\deploy-development.ps1 -SkipFrontend
```

### **Sadece Frontend Build Et:**
```powershell
.\deploy-development.ps1 -SkipBuild
```

### **Hiçbirini Build Etme (Sadece Environment Set Et):**
```powershell
.\deploy-development.ps1 -SkipBuild -SkipFrontend
```

## 🎮 **ADIM ADIM İLK KULLANIM**

### **Test Edelim:**

1. **Terminal Aç:**
   ```powershell
   # PowerShell'i Administrator olarak aç
   cd C:\Users\<USER>\Desktop\GymProject
   ```

2. **Development Script'ini Çalıştır:**
   ```powershell
   .\deploy-development.ps1
   ```

3. **Ne Olacak:**
   ```
   === GymKod Development Deployment ===
   ✅ Environment set to: Development
   ✅ Backend build successful!
   ✅ Frontend build successful!
   🚀 Ready to code! Happy Development! 🚀
   ```

4. **Uygulamayı Başlat:**
   ```powershell
   # Backend
   cd GymProjectBackend
   dotnet run --project WebAPI

   # Yeni terminal aç, Frontend
   cd GymProjectFrontend  
   ng serve
   ```

5. **Test Et:**
   - Backend: http://localhost:5165/api/
   - Frontend: http://localhost:4200

## ❓ **SORUN GİDERME**

### **Problem: "execution policy" hatası**
```powershell
# Çözüm:
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### **Problem: "ng command not found"**
```powershell
# Çözüm:
npm install -g @angular/cli
```

### **Problem: "dotnet command not found"**
- .NET 8 SDK'yı yükle: https://dotnet.microsoft.com/download

### **Problem: Script çalışmıyor**
```powershell
# Doğru klasörde olduğunu kontrol et:
pwd
# Çıktı: C:\Users\<USER>\Desktop\GymProject olmalı

# Script'in var olduğunu kontrol et:
ls *.ps1
```

## 🎊 **BAŞARILI DEPLOYMENT SONRASI**

### **Development:**
- ✅ Database: GymProject (Local)
- ✅ API: http://localhost:5165/api/
- ✅ Frontend: http://localhost:4200
- ✅ Swagger: Açık

### **Staging:**
- ✅ Database: Staging
- ✅ API: https://staging.gymkod.com/api/
- ✅ Frontend: https://staging.gymkod.com
- ✅ Swagger: Açık

### **Production:**
- ✅ Database: GymProject (LIVE)
- ✅ API: https://admin.gymkod.com/api/
- ✅ Frontend: https://admin.gymkod.com
- ❌ Swagger: Kapalı (Güvenlik)

## 🚀 **ÖZET**

**Artık sadece 3 komut bilmen yeterli:**

```powershell
.\deploy-development.ps1    # Geliştirme
.\deploy-staging.ps1        # Test
.\deploy-production.ps1     # Canlı
```

**Hiç kod değiştirmeyeceksin! 🎉**
