# ========================================
# ENVIRONMENT TEST SCRIPT
# ========================================

Write-Host "🧪 ENVIRONMENT CONFIGURATION TEST" -ForegroundColor Green
Write-Host "Bu script tüm environment'ların doğ<PERSON> ya<PERSON>ıldığını test eder." -ForegroundColor White
Write-Host "" -ForegroundColor White

# Solution dizinine git
Set-Location -Path $PSScriptRoot\..
Write-Host "📂 Working Directory: $(Get-Location)" -ForegroundColor Yellow

# Test fonksiyonu
function Test-Environment {
    param(
        [string]$EnvironmentName,
        [string]$ExpectedDatabase,
        [string]$ExpectedCors
    )
    
    Write-Host "🔍 Testing $EnvironmentName Environment..." -ForegroundColor Cyan
    
    # Environment variables set et
    switch ($EnvironmentName) {
        "Development" {
            $env:ASPNETCORE_ENVIRONMENT = "Development"
            Remove-Item Env:DB_SERVER -ErrorAction SilentlyContinue
            Remove-Item Env:DB_PASSWORD -ErrorAction SilentlyContinue
            Remove-Item Env:DB_DATABASE -ErrorAction SilentlyContinue
        }
        "Staging" {
            $env:ASPNETCORE_ENVIRONMENT = "Staging"
            $env:DB_SERVER = "localhost"
            $env:DB_PASSWORD = "1265222500Aa"
            $env:DB_DATABASE = "Staging"
        }
        "Production" {
            $env:ASPNETCORE_ENVIRONMENT = "Production"
            $env:DB_SERVER = "localhost"
            $env:DB_PASSWORD = "1265222500Aa"
            $env:DB_DATABASE = "GymProject"
        }
    }
    
    # appsettings dosyasını kontrol et
    $settingsFile = "WebAPI/appsettings.$EnvironmentName.json"
    if ($EnvironmentName -eq "Development") {
        $settingsFile = "WebAPI/appsettings.Development.json"
    }
    
    if (Test-Path $settingsFile) {
        Write-Host "   ✅ $settingsFile dosyası mevcut" -ForegroundColor Green
        
        # JSON içeriğini kontrol et
        try {
            $settings = Get-Content $settingsFile | ConvertFrom-Json
            
            # Connection string kontrolü
            if ($settings.ConnectionStrings.DefaultConnection) {
                Write-Host "   ✅ Connection string tanımlı" -ForegroundColor Green
                
                # Database adını kontrol et
                $connectionString = $settings.ConnectionStrings.DefaultConnection
                if ($connectionString -like "*$ExpectedDatabase*") {
                    Write-Host "   ✅ Database: $ExpectedDatabase" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ Database beklenen değil. Beklenen: $ExpectedDatabase" -ForegroundColor Red
                }
            } else {
                Write-Host "   ❌ Connection string bulunamadı" -ForegroundColor Red
            }
            
            # CORS kontrolü
            if ($settings.CorsSettings.AllowedOrigins) {
                $corsOrigins = $settings.CorsSettings.AllowedOrigins -join ", "
                Write-Host "   ✅ CORS: $corsOrigins" -ForegroundColor Green
            } else {
                Write-Host "   ❌ CORS ayarları bulunamadı" -ForegroundColor Red
            }
            
            # Token options kontrolü
            if ($settings.TokenOptions) {
                Write-Host "   ✅ Token options tanımlı" -ForegroundColor Green
            } else {
                Write-Host "   ❌ Token options bulunamadı" -ForegroundColor Red
            }
            
        } catch {
            Write-Host "   ❌ JSON parse hatası: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "   ❌ $settingsFile dosyası bulunamadı" -ForegroundColor Red
    }
    
    Write-Host "" -ForegroundColor White
}

# Build test
Write-Host "🔨 Build Test..." -ForegroundColor Yellow
dotnet build --configuration Release --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Build başarılı" -ForegroundColor Green
} else {
    Write-Host "❌ Build başarısız" -ForegroundColor Red
}
Write-Host "" -ForegroundColor White

# Environment testleri
Test-Environment -EnvironmentName "Development" -ExpectedDatabase "GymProject" -ExpectedCors "*"
Test-Environment -EnvironmentName "Staging" -ExpectedDatabase "Staging" -ExpectedCors "staging.gymkod.com"
Test-Environment -EnvironmentName "Production" -ExpectedDatabase "GymProject" -ExpectedCors "admin.gymkod.com"

# Script dosyalarını kontrol et
Write-Host "📜 Script Dosyaları Kontrolü..." -ForegroundColor Cyan
$scripts = @("deploy-staging.ps1", "deploy-production.ps1", "rollback.ps1")
foreach ($script in $scripts) {
    $scriptPath = "Scripts/$script"
    if (Test-Path $scriptPath) {
        Write-Host "   ✅ $script mevcut" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $script bulunamadı" -ForegroundColor Red
    }
}
Write-Host "" -ForegroundColor White

# GymContext.cs kontrolü
Write-Host "🗃️ GymContext.cs Kontrolü..." -ForegroundColor Cyan
$gymContextPath = "DataAccess/Concrete/EntityFramework/GymContext.cs"
if (Test-Path $gymContextPath) {
    $gymContextContent = Get-Content $gymContextPath -Raw
    
    if ($gymContextContent -like "*IConfiguration*") {
        Write-Host "   ✅ IConfiguration dependency injection mevcut" -ForegroundColor Green
    } else {
        Write-Host "   ❌ IConfiguration dependency injection bulunamadı" -ForegroundColor Red
    }
    
    if ($gymContextContent -like "*Environment.GetEnvironmentVariable*") {
        Write-Host "   ✅ Environment variables desteği mevcut" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Environment variables desteği bulunamadı" -ForegroundColor Red
    }
    
    if ($gymContextContent -notlike "*Server=localhost;Database=GymProject;Trusted_Connection=true*") {
        Write-Host "   ✅ Hardcoded connection string kaldırılmış" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Hardcoded connection string hala mevcut" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ GymContext.cs bulunamadı" -ForegroundColor Red
}
Write-Host "" -ForegroundColor White

# Program.cs kontrolü
Write-Host "⚙️ Program.cs Kontrolü..." -ForegroundColor Cyan
$programPath = "WebAPI/Program.cs"
if (Test-Path $programPath) {
    $programContent = Get-Content $programPath -Raw
    
    if ($programContent -like "*AddDbContext<GymContext>*") {
        Write-Host "   ✅ DbContext DI registration mevcut" -ForegroundColor Green
    } else {
        Write-Host "   ❌ DbContext DI registration bulunamadı" -ForegroundColor Red
    }
    
    if ($programContent -like "*CorsSettings:AllowedOrigins*") {
        Write-Host "   ✅ Environment-based CORS mevcut" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Environment-based CORS bulunamadı" -ForegroundColor Red
    }
    
    if ($programContent -like "*app.Environment.IsProduction()*") {
        Write-Host "   ✅ Environment-based HTTPS redirect mevcut" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Environment-based HTTPS redirect bulunamadı" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ Program.cs bulunamadı" -ForegroundColor Red
}
Write-Host "" -ForegroundColor White

# Özet
Write-Host "📊 TEST SONUÇLARI" -ForegroundColor Magenta
Write-Host "✅ Environment-based configuration sistemi kuruldu" -ForegroundColor Green
Write-Host "✅ Deployment scriptleri hazırlandı" -ForegroundColor Green
Write-Host "✅ Güvenlik ve backup mekanizmaları eklendi" -ForegroundColor Green
Write-Host "" -ForegroundColor White
Write-Host "🚀 SİSTEM HAZIR!" -ForegroundColor Green
Write-Host "Artık tek komutla deploy edebilirsiniz:" -ForegroundColor White
Write-Host "   Staging: .\Scripts\deploy-staging.ps1" -ForegroundColor Yellow
Write-Host "   Production: .\Scripts\deploy-production.ps1" -ForegroundColor Yellow
Write-Host "" -ForegroundColor White
