# ========================================
# ROLLBACK SCRIPT
# ========================================

param(
    [Parameter(Mandatory=$false)]
    [string]$BackupDate
)

Write-Host "🔄 ROLLBACK İŞLEMİ BAŞLIYOR..." -ForegroundColor Yellow
Write-Host "" -ForegroundColor White

$backupPath = "C:\Users\<USER>\Desktop\GymProject\api-backups"
$apiPath = "C:\Users\<USER>\Desktop\GymProject\api"

# Backup klasörünü kontrol et
if (!(Test-Path $backupPath)) {
    Write-Host "❌ Backup klasörü bulunamadı: $backupPath" -ForegroundColor Red
    exit 1
}

# Mevcut backup'ları listele
$backups = Get-ChildItem -Path $backupPath -Directory | Sort-Object Name -Descending

if ($backups.Count -eq 0) {
    Write-Host "❌ Hiç backup bulunamadı!" -ForegroundColor Red
    exit 1
}

Write-Host "📋 Mevcut Backup'lar:" -ForegroundColor Green
for ($i = 0; $i -lt $backups.Count; $i++) {
    $backup = $backups[$i]
    $date = $backup.Name -replace "api-backup-", ""
    $formattedDate = [DateTime]::ParseExact($date, "yyyyMMdd_HHmmss", $null).ToString("dd.MM.yyyy HH:mm:ss")
    Write-Host "   $($i + 1). $($backup.Name) ($formattedDate)" -ForegroundColor White
}
Write-Host "" -ForegroundColor White

# Backup seçimi
if ([string]::IsNullOrEmpty($BackupDate)) {
    $selection = Read-Host "Hangi backup'ı geri yüklemek istiyorsunuz? (1-$($backups.Count))"
    
    try {
        $index = [int]$selection - 1
        if ($index -lt 0 -or $index -ge $backups.Count) {
            Write-Host "❌ Geçersiz seçim!" -ForegroundColor Red
            exit 1
        }
        $selectedBackup = $backups[$index]
    }
    catch {
        Write-Host "❌ Geçersiz seçim!" -ForegroundColor Red
        exit 1
    }
} else {
    $selectedBackup = $backups | Where-Object { $_.Name -eq "api-backup-$BackupDate" }
    if (!$selectedBackup) {
        Write-Host "❌ Belirtilen backup bulunamadı: api-backup-$BackupDate" -ForegroundColor Red
        exit 1
    }
}

$selectedBackupPath = $selectedBackup.FullName
Write-Host "📦 Seçilen Backup: $($selectedBackup.Name)" -ForegroundColor Green

# Onay al
$confirmation = Read-Host "Bu backup'ı geri yüklemek istediğinizden emin misiniz? (y/N)"
if ($confirmation -ne 'y') {
    Write-Host "❌ Rollback İptal Edildi" -ForegroundColor Red
    exit 0
}

# Mevcut API'yi yedekle
$currentBackupDate = Get-Date -Format "yyyyMMdd_HHmmss"
$currentBackupPath = "$backupPath\api-current-$currentBackupDate"

Write-Host "💾 Mevcut API yedekleniyor..." -ForegroundColor Yellow
if (Test-Path $apiPath) {
    Copy-Item $apiPath $currentBackupPath -Recurse -Force
    Write-Host "✅ Mevcut API yedeklendi: api-current-$currentBackupDate" -ForegroundColor Green
}

# API klasörünü temizle
Write-Host "🧹 API klasörü temizleniyor..." -ForegroundColor Yellow
if (Test-Path $apiPath) {
    Remove-Item "$apiPath\*" -Recurse -Force
} else {
    New-Item -ItemType Directory -Path $apiPath -Force | Out-Null
}

# Backup'ı geri yükle
Write-Host "🔄 Backup geri yükleniyor..." -ForegroundColor Yellow
Copy-Item "$selectedBackupPath\*" $apiPath -Recurse -Force

# Dosya sayısını kontrol et
$fileCount = (Get-ChildItem -Path $apiPath -Recurse | Measure-Object).Count
Write-Host "📊 Geri yüklenen dosya sayısı: $fileCount" -ForegroundColor Green

# Başarı mesajı
Write-Host "" -ForegroundColor White
Write-Host "✅ ROLLBACK TAMAMLANDI!" -ForegroundColor Green
Write-Host "📁 Geri Yüklenen: $($selectedBackup.Name)" -ForegroundColor Cyan
Write-Host "📁 API Klasörü: $apiPath" -ForegroundColor Cyan
Write-Host "💾 Önceki Durum: api-current-$currentBackupDate" -ForegroundColor Cyan
Write-Host "" -ForegroundColor White
Write-Host "🔄 Sonraki Adımlar:" -ForegroundColor Magenta
Write-Host "   1. API klasörünü sunucuya kopyalayın" -ForegroundColor White
Write-Host "   2. Sunucuda: dotnet api/WebAPI.dll" -ForegroundColor White
Write-Host "   3. Sistemi test edin" -ForegroundColor White
Write-Host "" -ForegroundColor White
