# ========================================
# STAGING ENVIRONMENT DEPLOY SCRIPT
# ========================================

Write-Host "🚀 STAGING'E DEPLOY BAŞLIYOR..." -ForegroundColor Green
Write-Host "📍 Target: https://staging.gymkod.com" -ForegroundColor Cyan
Write-Host "🗃️ Database: Staging" -ForegroundColor Cyan
Write-Host "" -ForegroundColor White

# Staging environment variables set et
Write-Host "📝 Environment Variables Set Ediliyor..." -ForegroundColor Yellow
$env:ASPNETCORE_ENVIRONMENT = "Staging"
$env:DB_SERVER = "localhost"
$env:DB_PASSWORD = "1265222500Aa"
$env:DB_DATABASE = "Staging"

Write-Host "✅ Environment Variables:" -ForegroundColor Green
Write-Host "   - ASPNETCORE_ENVIRONMENT: $env:ASPNETCORE_ENVIRONMENT" -ForegroundColor White
Write-Host "   - DB_SERVER: $env:DB_SERVER" -ForegroundColor White
Write-Host "   - DB_DATABASE: $env:DB_DATABASE" -ForegroundColor White
Write-Host "   - DB_PASSWORD: ********" -ForegroundColor White
Write-Host "" -ForegroundColor White

# Solution dizinine git
Set-Location -Path $PSScriptRoot\..
Write-Host "📂 Working Directory: $(Get-Location)" -ForegroundColor Yellow

# Clean previous builds
Write-Host "🧹 Önceki Build'ler Temizleniyor..." -ForegroundColor Yellow
dotnet clean --configuration Release --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ Clean işlemi başarısız, devam ediliyor..." -ForegroundColor Yellow
}

# Restore packages
Write-Host "📦 NuGet Packages Restore Ediliyor..." -ForegroundColor Yellow
dotnet restore --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Package Restore Hatası!" -ForegroundColor Red
    exit 1
}

# Build project
Write-Host "🔨 Proje Build Ediliyor..." -ForegroundColor Yellow
dotnet build --configuration Release --no-restore --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build Hatası!" -ForegroundColor Red
    Write-Host "💡 Hata detayları için: dotnet build --configuration Release --verbosity normal" -ForegroundColor Yellow
    exit 1
}

# API klasörünü hazırla
$apiPath = "C:\Users\<USER>\Desktop\GymProject\api"
Write-Host "📁 API Klasörü Hazırlanıyor: $apiPath" -ForegroundColor Yellow

if (Test-Path $apiPath) {
    Write-Host "🧹 Mevcut API klasörü temizleniyor..." -ForegroundColor Yellow
    Remove-Item "$apiPath\*" -Recurse -Force -ErrorAction SilentlyContinue
} else {
    Write-Host "📁 API klasörü oluşturuluyor..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $apiPath -Force | Out-Null
}

# Portable publish
Write-Host "📦 Portable Publish Ediliyor..." -ForegroundColor Yellow
dotnet publish WebAPI/WebAPI.csproj `
    --configuration Release `
    --framework net8.0 `
    --runtime portable `
    --output $apiPath `
    --self-contained false `
    --no-build `
    --verbosity quiet

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Publish Hatası!" -ForegroundColor Red
    Write-Host "💡 Hata detayları için publish komutunu --verbosity normal ile çalıştırın" -ForegroundColor Yellow
    exit 1
}

# Dosya sayısını kontrol et
$fileCount = (Get-ChildItem -Path $apiPath -Recurse | Measure-Object).Count
Write-Host "📊 Publish edilen dosya sayısı: $fileCount" -ForegroundColor Green

# Başarı mesajı
Write-Host "" -ForegroundColor White
Write-Host "✅ STAGING DEPLOY TAMAMLANDI!" -ForegroundColor Green
Write-Host "📁 Dosyalar: $apiPath" -ForegroundColor Cyan
Write-Host "🌍 Site: https://staging.gymkod.com" -ForegroundColor Cyan
Write-Host "💡 Sunucuda çalıştırma: dotnet api/WebAPI.dll" -ForegroundColor Yellow
Write-Host "" -ForegroundColor White
Write-Host "🔄 Sonraki Adımlar:" -ForegroundColor Magenta
Write-Host "   1. API klasörünü sunucuya kopyalayın" -ForegroundColor White
Write-Host "   2. Sunucuda: dotnet api/WebAPI.dll" -ForegroundColor White
Write-Host "   3. https://staging.gymkod.com adresinden test edin" -ForegroundColor White
Write-Host "" -ForegroundColor White
