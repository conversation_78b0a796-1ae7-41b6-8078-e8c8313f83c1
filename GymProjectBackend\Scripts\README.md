# 🚀 GymProject Deployment Scripts

Bu klasör, GymProject Backend'ini farklı environment'lara deploy etmek için gerekli PowerShell scriptlerini içerir.

## 📁 Script Dosyaları

| Script | Açıklama |
|--------|----------|
| `deploy-staging.ps1` | Staging environment'a deploy |
| `deploy-production.ps1` | Production environment'a deploy |
| `rollback.ps1` | Önceki versiyona geri dönme |

## 🎯 Environment'lar

### 1. Development (Local)
- **Database**: GymProject (Trusted Connection)
- **CORS**: AllowAnyOrigin
- **HTTPS**: Devre dışı

### 2. Staging
- **URL**: https://staging.gymkod.com
- **Database**: Staging
- **CORS**: staging.gymkod.com
- **HTTPS**: Devre dışı

### 3. Production
- **URL**: https://admin.gymkod.com
- **Database**: GymProject
- **CORS**: admin.gymkod.com
- **HTTPS**: Zorunlu

## 🚀 Kullanım

### Staging'e Deploy
```powershell
cd C:\Users\<USER>\Desktop\GymProject\GymProjectBackend
.\Scripts\deploy-staging.ps1
```

### Production'a Deploy
```powershell
cd C:\Users\<USER>\Desktop\GymProject\GymProjectBackend
.\Scripts\deploy-production.ps1
```

### Rollback (Geri Alma)
```powershell
cd C:\Users\<USER>\Desktop\GymProject\GymProjectBackend
.\Scripts\rollback.ps1
```

## 📦 Deploy Süreci

1. **Environment Variables Set Etme**
2. **Clean & Restore**
3. **Build (Release Mode)**
4. **Backup (Production için)**
5. **Portable Publish**
6. **Dosyaları api/ klasörüne kopyalama**

## 📁 Çıktı Yapısı

Deploy sonrası dosyalar şu konumda olacak:
```
C:\Users\<USER>\Desktop\GymProject\
├── api/                    # Publish çıktısı
│   ├── WebAPI.dll
│   ├── appsettings.json
│   ├── appsettings.Staging.json
│   ├── appsettings.Production.json
│   └── ... (diğer dosyalar)
└── api-backups/           # Otomatik backup'lar
    ├── api-backup-20241230_143022/
    └── api-backup-20241230_151545/
```

## 🔧 Environment Variables

Scripts otomatik olarak şu environment variables'ları set eder:

### Staging
```
ASPNETCORE_ENVIRONMENT=Staging
DB_SERVER=localhost
DB_PASSWORD=1265222500Aa
DB_DATABASE=Staging
```

### Production
```
ASPNETCORE_ENVIRONMENT=Production
DB_SERVER=localhost
DB_PASSWORD=1265222500Aa
DB_DATABASE=GymProject
```

## 🛡️ Güvenlik Özellikleri

- **Production Onayı**: Production deploy için 'EVET' yazma zorunluluğu
- **Otomatik Backup**: Production deploy öncesi otomatik backup
- **Rollback**: Hata durumunda otomatik geri alma
- **Environment Isolation**: Her environment için ayrı ayarlar

## 🔄 Rollback Süreci

1. Mevcut durumu yedekle
2. Backup listesini göster
3. Kullanıcı seçimi al
4. Seçilen backup'ı geri yükle

## 💡 İpuçları

- Scripts'i Administrator olarak çalıştırın
- Deploy öncesi değişiklikleri commit edin
- Production deploy'dan önce Staging'de test edin
- Backup'ları düzenli olarak kontrol edin

## 🆘 Sorun Giderme

### Build Hatası
```powershell
# Detaylı hata için:
dotnet build --configuration Release --verbosity normal
```

### Publish Hatası
```powershell
# Manuel publish:
dotnet publish WebAPI/WebAPI.csproj --configuration Release --output ./api
```

### Connection String Hatası
- Environment variables'ların doğru set edildiğini kontrol edin
- Database'in erişilebilir olduğunu kontrol edin
