{"ConnectionStrings": {"DefaultConnection": "Server=localhost;User Id=sa;Password=${DB_PASSWORD};Database=GymProject;Trusted_Connection=false;Encrypt=False"}, "TokenOptions": {"Audience": "https://api.gymkod.com", "Issuer": "https://admin.gymkod.com", "AccessTokenExpiration": 2, "RefreshTokenExpiration": 30, "SecurityKey": "zX9c2Kf8Lm3Qp7Rt5Vw1Hy4Njt6Bd0Gs3Ae8Iu2Zy5Tx7Fq1Cm9Pk4Wv6Hn2Jl8Rd0Se3Gb7Mt"}, "CorsSettings": {"AllowedOrigins": ["https://admin.gymkod.com"]}, "IpRateLimiting": {"EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "RealIpHeader": "X-Real-IP", "ClientIdHeader": "X-ClientId", "HttpStatusCode": 429, "IpWhitelist": [], "EndpointWhitelist": ["get:/api/health", "*:/swagger/*", "post:/api/user/upload-profile-image"], "GeneralRules": [{"Endpoint": "*", "Period": "1m", "Limit": 200}, {"Endpoint": "*", "Period": "1h", "Limit": 2000}]}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Error"}}}